using System;
using System.IO;
using System.Text;

namespace StockMonitor.Utils
{
    public static class Logger
    {
        private static readonly object _lock = new object();

        /// <summary>
        /// 记录信息日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Info(string message)
        {
            WriteLog("INFO", message);
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Error(string message)
        {
            WriteLog("ERROR", message);
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        /// <param name="message">日志消息</param>
        public static void Debug(string message)
        {
            WriteLog("DEBUG", message);
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">日志消息</param>
        private static void WriteLog(string level, string message)
        {
            lock (_lock)
            {
                try
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var logMessage = $"[{timestamp}] [{level}] {message}";

                    // 使用控制台助手输出，自动处理中文编码问题
                    ConsoleHelper.WriteLine($"[{level}] {message}");

                    // 同时写入到调试输出
                    System.Diagnostics.Debug.WriteLine(logMessage);
                }
                catch (Exception ex)
                {
                    // 如果日志记录失败，尝试使用基本的输出方式
                    try
                    {
                        ConsoleHelper.WriteLine($"[ERROR] LOG_ERROR: {ex.Message}");
                        ConsoleHelper.WriteLine($"[ERROR] ORIGINAL_MESSAGE: {message}");
                    }
                    catch
                    {
                        // 如果连基本输出都失败，就忽略
                    }
                }
            }
        }



        /// <summary>
        /// 记录API调用开始
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="url">请求URL</param>
        public static void ApiStart(string apiName, string url)
        {
            Info($"{apiName} API请求开始: {url}");
        }

        /// <summary>
        /// 记录API调用完成
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="duration">耗时（毫秒）</param>
        /// <param name="responseLength">响应长度</param>
        public static void ApiComplete(string apiName, double duration, int responseLength)
        {
            Info($"{apiName} API请求完成，耗时: {duration:F0}ms，响应长度: {responseLength} 字符");
        }

        /// <summary>
        /// 记录API调用失败
        /// </summary>
        /// <param name="apiName">API名称</param>
        /// <param name="duration">耗时（毫秒）</param>
        /// <param name="error">错误信息</param>
        public static void ApiError(string apiName, double duration, string error)
        {
            Error($"{apiName} API调用失败，耗时: {duration:F0}ms，错误: {error}");
        }

        /// <summary>
        /// 记录用户操作
        /// </summary>
        /// <param name="action">操作描述</param>
        public static void UserAction(string action)
        {
            Info($"用户操作: {action}");
        }

        /// <summary>
        /// 记录搜索结果
        /// </summary>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="resultCount">结果数量</param>
        /// <param name="duration">耗时（毫秒）</param>
        public static void SearchResult(string keyword, int resultCount, double duration)
        {
            Info($"搜索完成 '{keyword}'，耗时: {duration:F0}ms，找到 {resultCount} 个结果");
        }
    }
}
