using System;
using System.Text;
using System.Windows.Forms;
using StockMonitor.Utils;

namespace StockMonitor
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 初始化控制台以支持中文显示
            ConsoleHelper.InitializeConsole();

            // 测试中文输出
            ConsoleHelper.WriteLine("股票监控程序启动");
            ConsoleHelper.WriteLine("中文编码测试: 这是一个测试消息");

            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 清理控制台资源
                ConsoleHelper.Cleanup();
            }
        }


    }
}
