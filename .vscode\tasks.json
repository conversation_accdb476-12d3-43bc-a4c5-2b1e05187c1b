{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/StockMonitor.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/StockMonitor.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "${workspaceFolder}/StockMonitor.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}