using System;
using System.IO;
using System.Text;
using System.Runtime.InteropServices;

namespace StockMonitor.Utils
{
    public static class ConsoleHelper
    {
        private static bool _isConsoleSetup = false;
        private static StreamWriter? _logFile = null;

        // Windows API 声明
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool AllocConsole();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeConsole();

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetStdHandle(int nStdHandle);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetConsoleCP(uint wCodePageID);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetConsoleOutputCP(uint wCodePageID);

        private const int STD_OUTPUT_HANDLE = -11;
        private const uint CP_UTF8 = 65001;
        private const uint CP_GBK = 936;

        /// <summary>
        /// 初始化控制台以支持中文显示
        /// </summary>
        public static void InitializeConsole()
        {
            if (_isConsoleSetup) return;

            try
            {
                // 备选方案：使用UTF-8
                SetConsoleCP(CP_UTF8);
                SetConsoleOutputCP(CP_UTF8);
                Console.OutputEncoding = Encoding.UTF8;
                Console.InputEncoding = Encoding.UTF8;
                _isConsoleSetup = true;
                Console.WriteLine("Console UTF-8 support enabled");
            }
            catch
            {
                // 最后备选：使用默认编码
                try
                {
                    Console.OutputEncoding = Encoding.Default;
                    Console.InputEncoding = Encoding.Default;
                    _isConsoleSetup = true;
                    Console.WriteLine("Console default encoding enabled");
                }
                catch
                {
                    _isConsoleSetup = false;
                }
            }

            // 初始化日志文件作为备选
            InitializeLogFile();
        }

        /// <summary>
        /// 初始化日志文件
        /// </summary>
        private static void InitializeLogFile()
        {
            try
            {
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
                if (!Directory.Exists(logPath))
                {
                    Directory.CreateDirectory(logPath);
                }

                var logFile = Path.Combine(logPath, $"stock_monitor_{DateTime.Now:yyyyMMdd}.log");
                _logFile = new StreamWriter(logFile, true, Encoding.UTF8);
                _logFile.AutoFlush = true;
            }
            catch
            {
                _logFile = null;
            }
        }

        /// <summary>
        /// 安全地输出中文文本到控制台
        /// </summary>
        /// <param name="message">要输出的消息</param>
        public static void WriteLine(string message)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var fullMessage = $"[{timestamp}] {message}";

            // 尝试输出到控制台
            bool consoleSuccess = false;
            try
            {
                Console.WriteLine(fullMessage);
                consoleSuccess = true;
            }
            catch
            {
                try
                {
                    // 尝试使用字节输出
                    var bytes = Encoding.GetEncoding("GBK").GetBytes(fullMessage + Environment.NewLine);
                    using (var stdout = Console.OpenStandardOutput())
                    {
                        stdout.Write(bytes, 0, bytes.Length);
                        stdout.Flush();
                    }
                    consoleSuccess = true;
                }
                catch
                {
                    // 控制台输出失败
                }
            }

            // 同时写入日志文件
            try
            {
                _logFile?.WriteLine(fullMessage);
            }
            catch
            {
                // 日志文件写入失败，忽略
            }

            // 如果控制台输出失败，尝试英文输出
            if (!consoleSuccess)
            {
                try
                {
                    var englishMessage = ConvertToEnglish(fullMessage);
                    Console.WriteLine(englishMessage);
                }
                catch
                {
                    // 完全失败，忽略
                }
            }
        }

        /// <summary>
        /// 将中文转换为英文（备用方案）
        /// </summary>
        private static string ConvertToEnglish(string message)
        {
            return message
                .Replace("用户操作", "User Action")
                .Replace("按回车键搜索", "Search by Enter")
                .Replace("开始搜索股票", "Start stock search")
                .Replace("API请求开始", "API request start")
                .Replace("API请求完成", "API request complete")
                .Replace("搜索完成", "Search complete")
                .Replace("显示搜索结果", "Show results")
                .Replace("未找到匹配的股票", "No stocks found")
                .Replace("搜索失败", "Search failed");
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                _logFile?.Close();
                _logFile?.Dispose();
            }
            catch
            {
                // 忽略清理错误
            }
        }
    }
}
