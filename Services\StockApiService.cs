using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Globalization;
using StockMonitor.Models;

namespace StockMonitor.Services
{
    public class StockApiService
    {
        private readonly HttpClient _httpClient;
        private static readonly StockApiService _instance = new StockApiService();

        public static StockApiService Instance => _instance;

        private StockApiService()
        {
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        }

        public async Task<List<StockSearchResult>> SearchStocksAsync(string keyword)
        {
            var results = new List<StockSearchResult>();
            var startTime = DateTime.Now;

            try
            {
                // 使用腾讯股票搜索API
                var url = $"https://smartbox.gtimg.cn/s3/?q={keyword}&t=all";
                Console.WriteLine($"[{startTime:yyyy-MM-dd HH:mm:ss.fff}] API请求开始: {url}");

                var response = await _httpClient.GetStringAsync(url);
                var requestEndTime = DateTime.Now;
                var requestDuration = requestEndTime - startTime;

                Console.WriteLine($"[{requestEndTime:yyyy-MM-dd HH:mm:ss.fff}] API请求完成，耗时: {requestDuration.TotalMilliseconds:F0}ms");
                Console.WriteLine($"[{requestEndTime:yyyy-MM-dd HH:mm:ss.fff}] 响应长度: {response.Length} 字符");
                Console.WriteLine($"[{requestEndTime:yyyy-MM-dd HH:mm:ss.fff}] 响应内容: {(response.Length > 200 ? response.Substring(0, 200) + "..." : response)}");

                // 解析腾讯搜索结果
                // 格式: v_hint="sh000001~上证指数~000001~股票指数";
                var pattern = @"([a-z]{2}\d{6})~([^~]+)~(\d{6})~([^~""]+)";
                var matches = Regex.Matches(response, pattern);

                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] 正则匹配找到 {matches.Count} 个结果");

                foreach (Match match in matches)
                {
                    if (match.Groups.Count >= 4)
                    {
                        var stockResult = new StockSearchResult
                        {
                            Code = match.Groups[1].Value.ToUpper(),
                            Name = match.Groups[2].Value,
                            Market = match.Groups[1].Value.Substring(0, 2).ToUpper(),
                            Type = match.Groups[4].Value
                        };
                        results.Add(stockResult);

                        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] 解析股票: {stockResult.Code} - {stockResult.Name} ({stockResult.Market}, {stockResult.Type})");
                    }
                }

                var totalDuration = DateTime.Now - startTime;
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] 搜索API调用完成，总耗时: {totalDuration.TotalMilliseconds:F0}ms，返回 {results.Count} 个结果");
            }
            catch (Exception ex)
            {
                var errorTime = DateTime.Now;
                var errorDuration = errorTime - startTime;
                Console.WriteLine($"[{errorTime:yyyy-MM-dd HH:mm:ss.fff}] 搜索股票API调用失败，耗时: {errorDuration.TotalMilliseconds:F0}ms");
                Console.WriteLine($"[{errorTime:yyyy-MM-dd HH:mm:ss.fff}] 错误详情: {ex.GetType().Name}: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"[{errorTime:yyyy-MM-dd HH:mm:ss.fff}] 内部异常: {ex.InnerException.Message}");
                }
            }

            return results;
        }

        public async Task<StockInfo?> GetStockInfoAsync(string code)
        {
            try
            {
                // 使用新浪股票API获取实时数据
                var url = $"https://hq.sinajs.cn/list={code.ToLower()}";
                var response = await _httpClient.GetStringAsync(url);

                return ParseSinaStockData(code, response);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取股票数据失败 {code}: {ex.Message}");
                return null;
            }
        }

        public async Task<List<StockInfo>> GetStockInfosAsync(List<string> codes)
        {
            var results = new List<StockInfo>();
            var tasks = new List<Task<StockInfo?>>();

            foreach (var code in codes)
            {
                tasks.Add(GetStockInfoAsync(code));
            }

            var stockInfos = await Task.WhenAll(tasks);

            foreach (var stockInfo in stockInfos)
            {
                if (stockInfo != null)
                {
                    results.Add(stockInfo);
                }
            }

            return results;
        }

        private StockInfo? ParseSinaStockData(string code, string response)
        {
            try
            {
                // 解析新浪股票数据格式
                // var hq_str_sh000001="上证指数,3200.123,3195.456,3205.789,...";
                var pattern = @"hq_str_[^=]+=""([^""]+)""";
                var match = Regex.Match(response, pattern);

                if (!match.Success) return null;

                var data = match.Groups[1].Value;
                var fields = data.Split(',');

                if (fields.Length < 32) return null;

                var stockInfo = new StockInfo
                {
                    Code = code.ToUpper(),
                    Name = fields[0],
                    Yesterday = ParseDecimal(fields[2]),
                    CurrentPrice = ParseDecimal(fields[3]),
                    High = ParseDecimal(fields[4]),
                    Low = ParseDecimal(fields[5]),
                    LastUpdate = DateTime.Now
                };

                stockInfo.Change = stockInfo.CurrentPrice - stockInfo.Yesterday;
                stockInfo.ChangePercent = stockInfo.Yesterday > 0
                    ? (stockInfo.Change / stockInfo.Yesterday) * 100
                    : 0;

                return stockInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解析股票数据失败: {ex.Message}");
                return null;
            }
        }

        private decimal ParseDecimal(string value)
        {
            if (decimal.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out var result))
            {
                return result;
            }
            return 0;
        }
    }
}
